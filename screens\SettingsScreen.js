import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  View,
  Text,
  Switch,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  Pressable,
  FlatList,
  TextInput,
  ActivityIndicator,
  Image,
  Alert,
} from 'react-native';
import {
  signOut,
  updateEmail,
  reauthenticateWithCredential,
  EmailAuthProvider,
  updatePassword,
  sendEmailVerification,
} from 'firebase/auth';
import { auth, db, getUserProfile } from '../firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { collection, addDoc, serverTimestamp, doc, updateDoc } from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Tanımlı dil seçenekleri
const LANGUAGES = [
  { key: 'tr', label: 'Türkçe' },
  { key: 'en', label: 'English' },
];

// Avatar mapping: ProfileEditScreen’de "avatarX" şeklinde kaydediliyor.
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

// MessageModal – uyarı mesajlarını şık şekilde göstermek için
function MessageModal({ visible, message, onClose }) {
  return (
    <Modal visible={visible} transparent animationType="fade">
      <Pressable style={styles.msgOverlay} onPress={onClose}>
        <View style={styles.msgContainer}>
          <Text style={styles.msgText}>{message}</Text>
          <TouchableOpacity style={styles.msgButton} onPress={onClose}>
            <Text style={styles.msgButtonText}>Tamam</Text>
          </TouchableOpacity>
        </View>
      </Pressable>
    </Modal>
  );
}

// Hesap kartlarını gruplamak için
function AccountCard({ title, children }) {
  return (
    <View style={styles.card}>
      <Text style={styles.cardTitle}>{title}</Text>
      {children}
    </View>
  );
}

export default function SettingsScreen() {
  const navigation = useNavigation();

  // Theme ve Language context'lerini kullan
  const { theme } = useTheme();
  const { language, changeLanguage, translations } = useLanguage();

  // Genel ayarlar
  const [isNotificationEnabled, setIsNotificationEnabled] = useState(false);
  const [isPrivateAccount, setIsPrivateAccount] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Dil ayarı
  const [selectedLanguage, setSelectedLanguage] = useState(language || 'tr');
  const [languageModalVisible, setLanguageModalVisible] = useState(false);

  // Yardım & Destek
  const [helpModalVisible, setHelpModalVisible] = useState(false);
  const [helpMessage, setHelpMessage] = useState('');

  // Hesap bilgileri (Firestore’dan alınan bilgiler)
  const [creationDate, setCreationDate] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userName, setUserName] = useState(''); // Firestore’dan gelen isim; yoksa "İsim Yok"
  const [profilePic, setProfilePic] = useState(null); // profilePic Firestore'dan alınan alan

  // E‑posta güncelleme için
  const [newEmail, setNewEmail] = useState('');

  // Şifre değiştirme için
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');

  // Modal görünürlükleri
  const [accountInfoModalVisible, setAccountInfoModalVisible] = useState(false);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [messageModalVisible, setMessageModalVisible] = useState(false);

  const [messageModalText, setMessageModalText] = useState('');
  const appVersion = "1.0.0";

  useEffect(() => {
    if (auth.currentUser) {
      setUserEmail(auth.currentUser.email);
      getUserProfile(auth.currentUser.uid)
        .then(profile => {
          const name = profile && profile.username
            ? profile.username
            : (auth.currentUser.displayName ? auth.currentUser.displayName : "İsim Yok");
          setUserName(name);
          if (profile && profile.createdAt) {
            setCreationDate(profile.createdAt.toDate().toLocaleString());
          } else if (auth.currentUser && auth.currentUser.metadata && auth.currentUser.metadata.creationTime) {
            // Firestore'da oluşturma tarihi yoksa Firebase Auth metadata'sından alalım
            setCreationDate(auth.currentUser.metadata.creationTime);
          }
          if (profile && profile.profilePic) {
            setProfilePic(profile.profilePic);
          }
          // Admin kontrolü
          if (profile && profile.isAdmin) {
            setIsAdmin(true);
          }
        })
        .catch(error => {
          console.error("Profil verileri çekilemedi: ", error);
          Alert.alert("Hata", "Profil verileri çekilemedi.");
        });
    }
  }, []);

  const handleHelpSubmit = async () => {
    if (helpMessage.trim() === '') {
      showMessage("Lütfen sorunuzu detaylıca yazınız.");
      return;
    }
    try {
      const currentUser = auth.currentUser;
      await addDoc(collection(db, "supportMessages"), {
        message: helpMessage,
        userId: currentUser ? currentUser.uid : "anonymous",
        createdAt: serverTimestamp(),
      });
      setHelpMessage('');
      setHelpModalVisible(false);
      showMessage("Mesajınız bize ulaştı. En kısa sürede dönüş yapacağız.");
    } catch (e) {
      showMessage("Mesaj gönderilemedi: " + e.message);
    }
  };

  const showMessage = (msg) => {
    setMessageModalText(msg);
    setMessageModalVisible(true);
  };

  const handleToggleNotifications = () => {
    setIsNotificationEnabled(prev => !prev);
  };
  const handleTogglePrivacy = async () => {
    const newValue = !isPrivateAccount;
    setIsPrivateAccount(newValue);

    // Firestore'a kaydet
    if (auth.currentUser) {
      try {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userRef, {
          isPrivateAccount: newValue
        });
      } catch (error) {
        console.error('Gizli hesap ayarı kaydedilirken hata:', error);
      }
    }
  };

  const handleLanguageChange = () => {
    // İngilizce dil seçimi için uyarı göster
    Alert.alert(
      "Dil Seçimi",
      "İngilizce dil desteği yakında eklenecektir. Şu an sadece Türkçe kullanılabilir.",
      [{ text: "Tamam", style: "default" }]
    );
  };
  const closeLanguageModal = () => {
    setLanguageModalVisible(false);
  };
  const selectLanguage = (langKey) => {
    setSelectedLanguage(langKey);
    changeLanguage(langKey);
    setLanguageModalVisible(false);
  };

  const handleAccountDetails = () => {
    setAccountInfoModalVisible(true);
  };

  const openEmailModal = () => {
    setNewEmail('');
    setEmailModalVisible(true);
  };

  const handleEmailUpdate = async () => {
    if (!newEmail.trim()) {
      showMessage("Yeni e‑posta boş olamaz.");
      return;
    }
    if (newEmail.length > 100) {
      showMessage("Yeni e‑posta en fazla 100 karakter olabilir.");
      return;
    }
    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(newEmail)) {
      showMessage("Geçerli bir e‑posta adresi giriniz.");
      return;
    }
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        showMessage("Güncelleme başarısız: Kullanıcı bulunamadı.");
        return;
      }
      if (!currentUser.emailVerified) {
        showMessage("Mevcut e‑postalınız doğrulanmamış. Lütfen önce e‑postanızı doğrulayın.");
        return;
      }
      await updateEmail(currentUser, newEmail);
      await sendEmailVerification(currentUser);
      showMessage("Yeni e‑posta adresinize doğrulama e‑postası gönderildi. Lütfen doğrulayın.");
      setEmailModalVisible(false);
    } catch (err) {
      showMessage("E‑posta güncelleme hatası: " + err.message);
    }
  };

  const openPasswordModal = () => {
    setOldPassword('');
    setNewPassword('');
    setPasswordModalVisible(true);
  };

  const handlePasswordUpdate = async () => {
    if (!oldPassword.trim() || !newPassword.trim()) {
      showMessage("Eski ve yeni şifre alanları boş olamaz.");
      return;
    }
    if (newPassword.length > 50) {
      showMessage("Yeni şifre en fazla 50 karakter olabilir.");
      return;
    }
    const passwordRegex = /^(?=.*[A-Z])(?=.*\d).{8,50}$/;
    if (!passwordRegex.test(newPassword)) {
      showMessage("Yeni şifreniz; en az 8 karakter, 1 büyük harf ve 1 rakam içermelidir.");
      return;
    }
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        showMessage("Şifre güncelleme başarısız: Kullanıcı bulunamadı.");
        return;
      }
      const credential = EmailAuthProvider.credential(currentUser.email, oldPassword);
      await reauthenticateWithCredential(currentUser, credential);
      await updatePassword(currentUser, newPassword);
      showMessage("Şifreniz başarıyla güncellendi.");
      setPasswordModalVisible(false);
    } catch (err) {
      showMessage("Şifre güncelleme hatası: " + err.message);
    }
  };

  const handleLogout = async () => {
    try {
      // "Beni hatırla" seçeneği kontrol et
      const rememberMeEnabled = await AsyncStorage.getItem('rememberMeEnabled');

      // Eğer "Beni hatırla" aktif değilse, şifre bilgilerini temizle
      if (rememberMeEnabled !== 'true') {
        await AsyncStorage.removeItem('rememberedEmail');
        await AsyncStorage.removeItem('rememberedPassword');
      }

      // Diğer kullanıcı bilgilerini temizle
      await AsyncStorage.removeItem('rememberedUser');

      // Firebase'den çıkış yap
      await signOut(auth);
      showMessage("Başarıyla çıkış yapıldı.");
    } catch (error) {
      showMessage("Çıkış yapılırken hata oluştu: " + error.message);
    }
  };

  const openUserAgreement = () => {
    navigation.navigate('TermsScreen');
  };

  const openPrivacyPolicy = () => {
    navigation.navigate('PrivacyPolicyScreen');
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Text style={[styles.headerText, { color: theme.text }]}>{translations.settings}</Text>

      {/* Profil Bilgileri */}
      <View style={styles.profileHeader}>
        <Image
          style={styles.profileImage}
          source={
            profilePic
              ? (avatarMap[profilePic] || require('../assets/default-avatar.png'))
              : require('../assets/default-avatar.png')
          }
        />
        <View style={styles.profileInfo}>
          <Text style={styles.profileName}>{userName || "İsim Yok"}</Text>
          <Text style={styles.profileEmail}>{userEmail || "E-posta"}</Text>
          {/* Oluşturma tarihi ana ekranda gösterilmeyecek */}
        </View>
      </View>

      <AccountCard title={translations.generalSettings}>
        <View style={styles.settingRow}>
          <Text style={[styles.settingLabel, { color: theme.text }]}>{translations.notifications}</Text>
          <Switch
            value={isNotificationEnabled}
            onValueChange={handleToggleNotifications}
            thumbColor={isNotificationEnabled ? '#1DA1F2' : '#ccc'}
            trackColor={{ false: '#767577', true: '#1DA1F2' }}
          />
        </View>
        <View style={styles.settingRow}>
          <Text style={[styles.settingLabel, { color: theme.text }]}>{translations.privateAccount}</Text>
          <Switch
            value={isPrivateAccount}
            onValueChange={handleTogglePrivacy}
            thumbColor={isPrivateAccount ? '#1DA1F2' : '#ccc'}
            trackColor={{ false: '#767577', true: '#1DA1F2' }}
          />
        </View>
        {/* Koyu tema seçeneği kaldırıldı - uygulama her zaman koyu tema kullanıyor */}
        <TouchableOpacity style={styles.settingButton} onPress={handleLanguageChange}>
          <Text style={styles.settingButtonText}>
            Dil Seçimi (Şu an: {selectedLanguage.toUpperCase()})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={() => navigation.navigate('BlockedUsers')}>
          <Text style={styles.settingButtonText}>
            Engellenen Kullanıcılar
          </Text>
        </TouchableOpacity>
      </AccountCard>

      <AccountCard title="Kaydedilen İçerikler">
        <TouchableOpacity style={styles.settingButton} onPress={() => navigation.navigate('SavedPosts')}>
          <Text style={styles.settingButtonText}>
            Kaydedilen Gönderiler
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={() => navigation.navigate('LikedPosts')}>
          <Text style={styles.settingButtonText}>
            Beğenilen Gönderiler
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={() => navigation.navigate('CommentedPosts')}>
          <Text style={styles.settingButtonText}>
            Yorum Yapılan Gönderiler
          </Text>
        </TouchableOpacity>
      </AccountCard>

      <AccountCard title="Hesap Ayarları">
        <TouchableOpacity style={styles.settingButton} onPress={() => setAccountInfoModalVisible(true)}>
          <Text style={styles.settingButtonText}>Hesap Bilgilerini Görüntüle</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={openEmailModal}>
          <Text style={styles.settingButtonText}>E-Postayı Güncelle</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={openPasswordModal}>
          <Text style={styles.settingButtonText}>Şifreyi Değiştir</Text>
        </TouchableOpacity>
      </AccountCard>

      <AccountCard title="Diğer">
        <TouchableOpacity style={styles.settingButton} onPress={() => navigation.navigate('PopularityInfo')}>
          <Text style={styles.settingButtonText}>Popülerlik ve Ünvanlar</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={openUserAgreement}>
          <Text style={styles.settingButtonText}>Kullanıcı Sözleşmesi</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.settingButton} onPress={openPrivacyPolicy}>
          <Text style={styles.settingButtonText}>Gizlilik Politikası</Text>
        </TouchableOpacity>
        {isAdmin && (
          <TouchableOpacity
            style={[styles.settingButton, styles.adminButton]}
            onPress={() => navigation.navigate('RankSettings')}
          >
            <Text style={styles.settingButtonText}>Ünvan Ayarları (Admin)</Text>
          </TouchableOpacity>
        )}
      </AccountCard>

      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutButtonText}>Çıkış Yap</Text>
      </TouchableOpacity>

      <Modal visible={languageModalVisible} transparent animationType="fade" onRequestClose={closeLanguageModal}>
        <Pressable style={styles.modalOverlay} onPress={closeLanguageModal}>
          <View style={styles.modalContainer}>
            <FlatList
              data={LANGUAGES}
              keyExtractor={(item) => item.key}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.languageOption} onPress={() => selectLanguage(item.key)}>
                  <Text style={styles.languageOptionText}>{item.label}</Text>
                  {selectedLanguage === item.key && (
                    <Ionicons name="checkmark" size={20} color="#1DA1F2" style={{ marginLeft: 8 }} />
                  )}
                </TouchableOpacity>
              )}
            />
            <TouchableOpacity style={[styles.settingButton, { marginTop: 20 }]} onPress={closeLanguageModal}>
              <Text style={styles.settingButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </Pressable>
      </Modal>

      <Modal visible={emailModalVisible} transparent animationType="slide" onRequestClose={() => setEmailModalVisible(false)}>
        <View style={styles.modalOverlay2}>
          <View style={styles.accountModalContainer}>
            <Text style={styles.accountModalTitle}>E-Postayı Güncelle</Text>
            <TextInput
              style={styles.inputEmail}
              placeholder="Yeni e-postanızı giriniz"
              placeholderTextColor="#888"
              value={newEmail}
              onChangeText={setNewEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <TouchableOpacity style={styles.smallButton} onPress={handleEmailUpdate}>
              <Text style={styles.smallButtonText}>Güncelle</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.settingButton} onPress={() => setEmailModalVisible(false)}>
              <Text style={styles.settingButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal visible={passwordModalVisible} transparent animationType="slide" onRequestClose={() => setPasswordModalVisible(false)}>
        <View style={styles.modalOverlay2}>
          <View style={styles.accountModalContainer}>
            <Text style={styles.accountModalTitle}>Şifreyi Değiştir</Text>
            <TextInput
              style={styles.inputEmail}
              placeholder="Eski şifrenizi giriniz"
              placeholderTextColor="#888"
              secureTextEntry
              value={oldPassword}
              onChangeText={setOldPassword}
            />
            <TextInput
              style={styles.inputEmail}
              placeholder="Yeni şifre (En az 8 karakter, 1 büyük harf, 1 rakam)"
              placeholderTextColor="#888"
              secureTextEntry
              value={newPassword}
              onChangeText={setNewPassword}
            />
            <TouchableOpacity style={styles.smallButton} onPress={handlePasswordUpdate}>
              <Text style={styles.smallButtonText}>Güncelle</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.settingButton} onPress={() => setPasswordModalVisible(false)}>
              <Text style={styles.settingButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal visible={accountInfoModalVisible} transparent animationType="slide" onRequestClose={() => setAccountInfoModalVisible(false)}>
        <View style={styles.modalOverlay2}>
          <View style={styles.accountModalContainer}>
            <ScrollView>
              <Text style={styles.accountModalTitle}>Hesap Bilgileri</Text>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Kullanıcı Adı:</Text>
                <Text style={styles.infoValue}>{userName}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>E-Posta:</Text>
                <Text style={styles.infoValue}>{userEmail}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Hesap Oluşturma:</Text>
                <Text style={styles.infoValue}>{creationDate}</Text>
              </View>
            </ScrollView>
            <TouchableOpacity style={styles.settingButton} onPress={() => setAccountInfoModalVisible(false)}>
              <Text style={styles.settingButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal visible={helpModalVisible} transparent animationType="slide" onRequestClose={() => setHelpModalVisible(false)}>
        <View style={styles.modalOverlay2}>
          <View style={styles.accountModalContainer}>
            <Text style={styles.accountModalTitle}>Yardım ve Destek</Text>
            <TextInput
              style={[styles.inputEmail, { height: 100, textAlignVertical: 'top' }]}
              placeholder="Sorununuzu buraya yazınız"
              placeholderTextColor="#888"
              multiline
              value={helpMessage}
              onChangeText={setHelpMessage}
            />
            <TouchableOpacity style={styles.smallButton} onPress={handleHelpSubmit}>
              <Text style={styles.smallButtonText}>Gönder</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.settingButton, { marginTop: 20 }]} onPress={() => setHelpModalVisible(false)}>
              <Text style={styles.settingButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <MessageModal visible={messageModalVisible} message={messageModalText} onClose={() => setMessageModalVisible(false)} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#000' },
  contentContainer: { padding: 20, paddingBottom: 40 },
  headerText: { fontSize: 28, color: '#fff', fontWeight: 'bold', marginBottom: 30, textAlign: 'center' },
  profileHeader: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#222', borderRadius: 10, padding: 15, marginBottom: 20 },
  profileImage: { width: 70, height: 70, borderRadius: 35, borderWidth: 2, borderColor: '#1DA1F2' },
  profileInfo: { marginLeft: 15 },
  profileName: { fontSize: 20, color: '#fff', fontWeight: '600' },
  profileEmail: { fontSize: 14, color: '#aaa', marginTop: 4 },
  appVersion: { fontSize: 12, color: '#888', marginTop: 6 },
  card: { backgroundColor: '#111', borderRadius: 10, padding: 15, marginBottom: 20 },
  cardTitle: { fontSize: 20, color: '#1DA1F2', fontWeight: 'bold', marginBottom: 10, textAlign: 'center' },
  settingRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 8 },
  settingLabel: { fontSize: 16, color: '#fff' },
  settingButton: { backgroundColor: '#1DA1F2', paddingVertical: 15, paddingHorizontal: 20, borderRadius: 10, marginBottom: 20 },
  settingButtonText: { color: '#fff', fontSize: 18, textAlign: 'center' },
  logoutButton: { backgroundColor: '#FF3B30', paddingVertical: 15, borderRadius: 10, marginTop: 10 },
  logoutButtonText: { color: '#fff', fontSize: 18, textAlign: 'center' },
  modalOverlay: { flex: 1, backgroundColor: 'rgba(0,0,0,0.7)', justifyContent: 'center', alignItems: 'center' },
  modalContainer: { backgroundColor: '#111', width: '80%', borderRadius: 10, padding: 20 },
  languageOption: { flexDirection: 'row', alignItems: 'center', paddingVertical: 10, borderBottomColor: '#333', borderBottomWidth: 1, justifyContent: 'space-between' },
  languageOptionText: { color: '#fff', fontSize: 16 },
  modalOverlay2: { flex: 1, backgroundColor: 'rgba(0,0,0,0.8)', justifyContent: 'center' },
  accountModalContainer: { marginHorizontal: 20, borderRadius: 10, padding: 20, maxHeight: '80%', backgroundColor: '#1a1a1a' },
  accountModalTitle: { fontSize: 22, color: '#fff', fontWeight: 'bold', marginBottom: 15, textAlign: 'center' },
  infoRow: { flexDirection: 'row', marginBottom: 10, alignItems: 'center' },
  infoLabel: { color: '#fff', marginRight: 10, fontSize: 14, width: 140 },
  infoValue: { color: '#fff', fontSize: 14, flex: 1 },
  inputEmail: { backgroundColor: '#333', color: '#fff', padding: 10, borderRadius: 8, marginBottom: 10, fontSize: 16 },
  smallButton: { alignSelf: 'flex-start', backgroundColor: '#1DA1F2', paddingHorizontal: 12, paddingVertical: 8, borderRadius: 6, marginBottom: 10 },
  smallButtonText: { color: '#fff', fontSize: 14 },
  msgOverlay: { flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', alignItems: 'center' },
  msgContainer: { backgroundColor: '#222', padding: 20, borderRadius: 10, width: '75%', alignItems: 'center' },
  msgText: { color: '#fff', fontSize: 16, marginBottom: 20, textAlign: 'center' },
  msgButton: { backgroundColor: '#1DA1F2', paddingVertical: 10, paddingHorizontal: 20, borderRadius: 6 },
  msgButtonText: { color: '#fff', fontSize: 16 },
  adminButton: { backgroundColor: '#3b5998' },
  // Hesap bölümündeki gönderi kartı stilleri
  profileHeader: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#222', borderRadius: 10, padding: 15, marginBottom: 20 },
  profileImage: { width: 70, height: 70, borderRadius: 35, borderWidth: 2, borderColor: '#1DA1F2' },
  profileInfo: { marginLeft: 15 },
  profileName: { fontSize: 20, color: '#fff', fontWeight: '600' },
  profileEmail: { fontSize: 14, color: '#aaa', marginTop: 4 },
  appVersion: { fontSize: 12, color: '#888', marginTop: 6 },
  card: { backgroundColor: '#111', borderRadius: 10, padding: 15, marginBottom: 20 },
  cardTitle: { fontSize: 20, color: '#1DA1F2', fontWeight: 'bold', marginBottom: 10, textAlign: 'center' },
  settingRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginVertical: 8 },
  settingLabel: { fontSize: 16, color: '#fff' },
  settingButton: { backgroundColor: '#1DA1F2', paddingVertical: 15, paddingHorizontal: 20, borderRadius: 10, marginBottom: 20 },
  settingButtonText: { color: '#fff', fontSize: 18, textAlign: 'center' },
  logoutButton: { backgroundColor: '#FF3B30', paddingVertical: 15, borderRadius: 10, marginTop: 10 },
  logoutButtonText: { color: '#fff', fontSize: 18, textAlign: 'center' },
  modalOverlay2: { flex: 1, backgroundColor: 'rgba(0,0,0,0.8)', justifyContent: 'center' },
  accountModalContainer: { marginHorizontal: 20, borderRadius: 10, padding: 20, maxHeight: '80%', backgroundColor: '#1a1a1a' },
  accountModalTitle: { fontSize: 22, color: '#fff', fontWeight: 'bold', marginBottom: 15, textAlign: 'center' },
  infoRow: { flexDirection: 'row', marginBottom: 10, alignItems: 'center' },
  infoLabel: { color: '#fff', marginRight: 10, fontSize: 14, width: 140 },
  infoValue: { color: '#fff', fontSize: 14, flex: 1 },
});
