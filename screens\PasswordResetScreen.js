import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Platform,
  ToastAndroid,
} from 'react-native';
import { confirmPasswordReset } from 'firebase/auth';
import { auth } from '../firebase';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

const PasswordResetScreen = () => {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const route = useRoute();
  const navigation = useNavigation();
  const { oobCode } = route.params || {}; // Firebase'den gelen kod

  // <PERSON><PERSON><PERSON> gü<PERSON> kontrolü
  const checkPasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const handlePasswordChange = (text) => {
    setNewPassword(text);
    setPasswordStrength(checkPasswordStrength(text));
  };

  const validatePassword = () => {
    if (newPassword.length < 6) {
      Alert.alert('Hata', 'Şifre en az 6 karakter olmalıdır.');
      return false;
    }
    if (newPassword.length > 30) {
      Alert.alert('Hata', 'Şifre en fazla 30 karakter olabilir.');
      return false;
    }
    if (!/^[\x20-\x7E]*$/.test(newPassword)) {
      Alert.alert('Hata', 'Şifre sadece standart karakterler içerebilir (emoji kullanılamaz).');
      return false;
    }
    if (newPassword !== confirmPassword) {
      Alert.alert('Hata', 'Şifreler eşleşmiyor.');
      return false;
    }
    return true;
  };

  const handleResetPassword = async () => {
    if (!validatePassword()) return;

    setLoading(true);
    try {
      await confirmPasswordReset(auth, oobCode, newPassword);
      
      if (Platform.OS === 'android') {
        ToastAndroid.show('Şifreniz başarıyla sıfırlandı. Giriş yapabilirsiniz.', ToastAndroid.LONG);
      } else {
        Alert.alert('Başarılı', 'Şifreniz başarıyla sıfırlandı. Giriş yapabilirsiniz.');
      }
      
      navigation.navigate('Login');
    } catch (error) {
      let message = 'Şifre sıfırlama başarısız.';
      switch (error.code) {
        case 'auth/expired-action-code':
          message = 'Şifre sıfırlama linki süresi dolmuş. Yeni bir link talep edin.';
          break;
        case 'auth/invalid-action-code':
          message = 'Geçersiz şifre sıfırlama linki.';
          break;
        case 'auth/weak-password':
          message = 'Şifre çok zayıf. Daha güçlü bir şifre seçin.';
          break;
        default:
          message = error.message;
      }
      Alert.alert('Hata', message);
    } finally {
      setLoading(false);
    }
  };

  const getStrengthColor = () => {
    switch (passwordStrength) {
      case 0:
      case 1:
        return '#ff4444';
      case 2:
        return '#ffaa00';
      case 3:
      case 4:
        return '#00aa00';
      default:
        return '#cccccc';
    }
  };

  const getStrengthText = () => {
    switch (passwordStrength) {
      case 0:
      case 1:
        return 'Zayıf';
      case 2:
        return 'Orta';
      case 3:
      case 4:
        return 'Güçlü';
      default:
        return '';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.title}>Şifre Sıfırla</Text>
      </View>

      <View style={styles.form}>
        <Text style={styles.description}>
          Yeni şifrenizi belirleyin. Şifreniz 6-30 karakter olmalıdır. Emoji kullanılamaz.
        </Text>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Yeni Şifre (6-30 karakter)"
            placeholderTextColor="#888"
            secureTextEntry={!passwordVisible}
            value={newPassword}
            maxLength={30}
            onChangeText={(text) => {
              // Sadece ASCII karakterleri kabul et
              const filteredText = text.replace(/[^\x20-\x7E]/g, '');
              setNewPassword(filteredText);
              setPasswordStrength(checkPasswordStrength(filteredText));
            }}
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setPasswordVisible(!passwordVisible)}
          >
            <Ionicons
              name={passwordVisible ? 'eye-off' : 'eye'}
              size={20}
              color="#888"
            />
          </TouchableOpacity>
        </View>

        {newPassword.length > 0 && (
          <View style={styles.strengthContainer}>
            <View style={[styles.strengthBar, { backgroundColor: getStrengthColor() }]} />
            <Text style={[styles.strengthText, { color: getStrengthColor() }]}>
              {getStrengthText()}
            </Text>
          </View>
        )}

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Şifre Tekrar (6-30 karakter)"
            placeholderTextColor="#888"
            secureTextEntry={!confirmPasswordVisible}
            value={confirmPassword}
            maxLength={30}
            onChangeText={(text) => {
              // Sadece ASCII karakterleri kabul et
              const filteredText = text.replace(/[^\x20-\x7E]/g, '');
              setConfirmPassword(filteredText);
            }}
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
          >
            <Ionicons
              name={confirmPasswordVisible ? 'eye-off' : 'eye'}
              size={20}
              color="#888"
            />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={handleResetPassword}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Şifreyi Sıfırla</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 20,
  },
  form: {
    flex: 1,
    padding: 20,
  },
  description: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 30,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    borderRadius: 10,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#333',
  },
  input: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    padding: 15,
  },
  eyeButton: {
    padding: 15,
  },
  strengthContainer: {
    marginBottom: 15,
  },
  strengthBar: {
    height: 4,
    borderRadius: 2,
    marginBottom: 5,
  },
  strengthText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  button: {
    backgroundColor: '#3B82F6',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    backgroundColor: '#666',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default PasswordResetScreen;
